#!/usr/bin/env python3
"""
Verification script for Stage 8 manual navigation implementation.

This script verifies that Stage 8 (Script Optimization) now uses manual navigation
instead of automatic progression after optimization completion. Users should remain
in Stage 8 and choose their next action manually.
"""

import logging
from unittest.mock import Mock, patch
from state_manager import StateManager, StateStage

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("verify_stage8_to_stage3")


def test_stage8_manual_navigation():
    """Test that Stage 8 stays in Stage 8 after optimization completion and shows manual navigation options."""
    logger.info("=" * 80)
    logger.info("TESTING STAGE 8 MANUAL NAVIGATION (NO AUTOMATIC PROGRESSION)")
    logger.info("=" * 80)

    # Create a mock state manager
    state = Mock(spec=StateManager)
    state.current_stage = StateStage.STAGE8_OPTIMIZE
    state.advance_to = Mock(return_value=True)

    # Set up optimization completion state
    state.optimization_complete = True
    state.optimized_script_path = "generated_tests/test_TC001_optimized_12345.py"
    state.optimized_script_content = "# Optimized test script content"
    state.optimization_in_progress = False

    # Mock session state
    mock_session_state = {}

    logger.info("Test setup complete:")
    logger.info(f"  - Current stage: {state.current_stage}")
    logger.info(f"  - Optimization complete: {state.optimization_complete}")
    logger.info(f"  - Optimized script path: {state.optimized_script_path}")

    # Simulate the new manual navigation logic from stage8.py
    with patch('streamlit.session_state', mock_session_state):
        with patch('streamlit.rerun') as mock_rerun:
            logger.info("Simulating optimization completion logic...")

            # This is the new logic from stage8.py - NO automatic progression
            if state.current_stage == StateStage.STAGE8_OPTIMIZE:
                # Set success message for user feedback
                mock_session_state['stage_progression_message'] = "✅ Script optimization completed successfully. Choose your next action below."

                logger.info("Stage 8: Optimization completed - staying in Stage 8 for manual navigation")
                logger.info("Stage 8: Calling st.rerun() to display optimization results and navigation options")
                mock_rerun()

    # Verify NO automatic transition occurred
    logger.info("Verifying manual navigation behavior...")

    # Check that advance_to was NOT called automatically
    state.advance_to.assert_not_called()
    logger.info("✅ advance_to() was NOT called automatically - manual navigation working correctly")

    # Check that st.rerun() was called to refresh UI
    mock_rerun.assert_called_once()
    logger.info("✅ st.rerun() called to refresh UI and show navigation options")

    # Check session state was updated with manual navigation message
    expected_message = "✅ Script optimization completed successfully. Choose your next action below."
    assert mock_session_state['stage_progression_message'] == expected_message
    logger.info("✅ Session state progression message set correctly for manual navigation")

    # Verify stage remains Stage 8
    assert state.current_stage == StateStage.STAGE8_OPTIMIZE
    logger.info("✅ Current stage remains Stage 8 for manual navigation")

    logger.info("=" * 80)
    logger.info("✅ STAGE 8 MANUAL NAVIGATION TEST PASSED")
    logger.info("=" * 80)

    return True


def test_state_manager_stage8_to_stage3_support():
    """Test that StateManager properly supports Stage 8 → Stage 3 transitions."""
    logger.info("=" * 80)
    logger.info("TESTING STATE MANAGER STAGE 8 → STAGE 3 SUPPORT")
    logger.info("=" * 80)

    # Create a real StateManager instance
    state = StateManager()

    # Set up Stage 8 state
    state.current_stage = StateStage.STAGE8_OPTIMIZE
    state.optimization_complete = True
    state.optimized_script_path = "test_script.py"

    logger.info(f"Initial stage: {state.current_stage}")

    # Test direct advance_to call
    result = state.advance_to(StateStage.STAGE3_CONVERT, "Test Stage 8 → Stage 3 transition")

    # Verify transition was successful
    assert result == True, "advance_to should return True for legal transition"
    logger.info("✅ advance_to() returned True for Stage 8 → Stage 3 transition")

    assert state.current_stage == StateStage.STAGE3_CONVERT, f"Expected Stage 3, got {state.current_stage}"
    logger.info("✅ Current stage updated to Stage 3")

    # Test reset_test_case_state method
    state.current_stage = StateStage.STAGE8_OPTIMIZE  # Reset to Stage 8
    state.selected_test_case = {"Test Case ID": "TC001"}
    state.optimization_complete = True

    logger.info("Testing reset_test_case_state method...")
    result = state.reset_test_case_state(confirm=True, reason="Script optimization complete")

    assert result == True, "reset_test_case_state should return True"
    logger.info("✅ reset_test_case_state() returned True")

    assert state.current_stage == StateStage.STAGE3_CONVERT, f"Expected Stage 3 after reset, got {state.current_stage}"
    logger.info("✅ reset_test_case_state() correctly advanced to Stage 3")

    # Verify optimization state was cleared
    assert state.optimization_complete == False, "optimization_complete should be cleared"
    logger.info("✅ Optimization state properly cleared")

    assert state.selected_test_case == None, "selected_test_case should be cleared"
    logger.info("✅ Test case state properly cleared")

    logger.info("=" * 80)
    logger.info("✅ STATE MANAGER STAGE 8 → STAGE 3 SUPPORT TEST PASSED")
    logger.info("=" * 80)

    return True


def main():
    """Run all verification tests."""
    logger.info("Starting Stage 8 manual navigation verification...")

    try:
        # Test 1: Manual navigation logic (no automatic progression)
        test_stage8_manual_navigation()

        # Test 2: StateManager support for manual transitions
        test_state_manager_stage8_to_stage3_support()

        logger.info("🎉 ALL VERIFICATION TESTS PASSED!")
        logger.info("✅ Stage 8 manual navigation is working correctly")
        logger.info("✅ Users will remain in Stage 8 after optimization and choose their next action manually")

        return True

    except Exception as e:
        logger.error(f"❌ Verification failed: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
